{"name": "starter-next-ts-template", "description": "A Nextjs starter template", "version": "0.1.0", "private": true, "author": "Nextjs", "license": "Apache-2.0", "scripts": {"dev": "next dev -p 3457", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@number-flow/react": "^0.5.7", "@reduxjs/toolkit": "^1.9.3", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.44", "@table-library/react-table-library": "^4.1.15", "@types/emoji-mart": "^5.3.0", "@types/styled-components": "^5.1.26", "assert": "^2.0.0", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "cannon-es": "^0.20.0", "cannon-es-debugger": "^1.0.0", "canvas-confetti": "^1.9.3", "cbor": "^9.0.2", "crypto-js": "^4.2.0", "dat.gui": "^0.7.9", "dayjs": "^1.11.13", "disable-devtool": "^0.3.8", "ecctrl": "^1.0.91", "emoji-mart": "^5.6.0", "es-toolkit": "^1.37.2", "gsap": "^3.12.5", "howler": "^2.2.4", "leva": "^0.9.35", "lodash": "^4.17.21", "ls-compressimage": "^1.0.1", "marked": "^15.0.7", "marked-custom-heading-id": "^2.0.11", "motion": "^12.4.10", "next": "^15.4.0-canary.51", "node-rsa": "^1.1.1", "protobufjs": "^7.5.3", "r3f-perf": "^7.2.3", "rc-pagination": "^4.3.0", "rc-slider": "^11.1.7", "rc-tooltip": "^6.3.2", "react": "18.2.0", "react-colorful": "^5.6.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "18.2.0", "react-hot-toast": "^2.4.1", "react-lottie": "^1.2.4", "react-masonry-css": "^1.0.16", "react-redux": "^8.0.5", "react-slick": "^0.30.3", "react-turnstile": "^1.1.4", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "styled-components": "^5.3.6", "three": "^0.177.0", "three-mesh-ui": "^6.5.4", "three-stdlib": "^2.35.14", "url-loader": "^4.1.1", "zustand": "^5.0.1"}, "devDependencies": {"@react-three/drei": "^9.117.3", "@react-three/fiber": "^8.17.10", "@react-three/rapier": "^1.5.0", "@rollup/plugin-typescript": "8.4.0", "@types/canvas-confetti": "^1.9.0", "@types/crypto-js": "^4.2.2", "@types/dat.gui": "^0.7.13", "@types/howler": "^2.2.12", "@types/lodash": "^4.14.191", "@types/node": "18.11.9", "@types/node-rsa": "^1.1.4", "@types/react": "^18.0.25", "@types/react-dom": "18.0.8", "@types/react-slick": "^0.23.13", "@types/three": "^0.171.0", "@typescript-eslint/eslint-plugin": "5.36.1", "@typescript-eslint/parser": "5.36.1", "dom-to-image-more": "^3.5.0", "eslint": "8.27.0", "eslint-config-next": "13.0.2", "husky": "8.0.1", "rollup": "^2.79.2", "tslib": "2.4.0", "typescript": "4.8.4", "vite": "^6.2.6", "vite-plugin-eslint": "1.8.1", "vite-plugin-wasm": "^3.4.1", "vitest": "^3.1.1"}, "browser": {"fs": false, "path": false, "os": false, "http2": false, "net": false, "tls": false, "dns": false}, "resolutions": {"styled-components": "^5"}}